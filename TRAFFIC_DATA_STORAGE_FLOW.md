# 🗂️ 分组流量数据存储位置和流程详解

## 📍 数据存储位置概览

### 1. **临时存储（内存）**
- **位置**: `client_traffic_map` 字典变量
- **生命周期**: 单次收集任务期间
- **作用**: 缓存分组内所有客户端的流量数据

### 2. **永久存储（数据库）**
- **表名**: `traffic_stats`
- **字段**: 包含 `group_id` 用于按分组统计
- **生命周期**: 长期保存，可配置清理策略

## 🔄 完整数据流程

### 阶段1: 从X-UI面板获取原始数据
```
X-UI面板 → MultiXUIManager.get_all_client_traffic() → 原始流量数据
```

**数据格式**:
```python
all_traffic_data = {
    'panel_1': [
        {'email': '<EMAIL>', 'up': 1024, 'down': 2048, 'total': 3072},
        {'email': '<EMAIL>', 'up': 512, 'down': 1024, 'total': 1536},
        # ... 更多客户端
    ],
    'panel_2': [
        # ... 其他面板的客户端数据
    ]
}
```

### 阶段2: 内存缓存处理
**位置**: `_collect_group_traffic()` 方法中的 `client_traffic_map`

```python
# 构建客户端邮箱到流量数据的映射
client_traffic_map = {
    '<EMAIL>': {
        'upload_bytes': 1024,
        'download_bytes': 2048,
        'total_bytes': 3072
    },
    '<EMAIL>': {
        'upload_bytes': 512,
        'download_bytes': 1024,
        'total_bytes': 1536
    }
    # ... 更多客户端映射
}
```

**特点**:
- ✅ 自动合并多面板中同一客户端的流量
- ✅ 按邮箱去重，避免重复计算
- ✅ 临时存储，处理完即释放

### 阶段3: 订阅流量匹配
**位置**: `_match_subscription_traffic()` 方法

```python
# 从缓存中匹配订阅的客户端流量
for client_email in client_emails:
    if client_email in client_traffic_map:
        traffic_data = client_traffic_map[client_email]
        total_up += traffic_data['upload_bytes']
        total_down += traffic_data['download_bytes']
        total_traffic += traffic_data['total_bytes']
```

### 阶段4: 数据库永久存储
**位置**: `TrafficStats` 表

```sql
INSERT INTO traffic_stats (
    user_id,
    subscription_id,
    group_id,           -- 🔑 关键字段：分组ID
    upload_bytes,
    download_bytes,
    total_bytes,
    recorded_at
) VALUES (?, ?, ?, ?, ?, ?, ?);
```

## 🏗️ 数据库表结构

### TrafficStats 表
```sql
CREATE TABLE traffic_stats (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,                    -- 用户ID
    subscription_id INTEGER NOT NULL,            -- 订阅ID
    group_id INTEGER,                           -- 🔑 分组ID (可为NULL)
    upload_bytes BIGINT NOT NULL DEFAULT 0,     -- 上传流量(字节)
    download_bytes BIGINT NOT NULL DEFAULT 0,   -- 下载流量(字节)
    total_bytes BIGINT NOT NULL DEFAULT 0,      -- 总流量(字节)
    recorded_at DATETIME NOT NULL,              -- 记录时间
    created_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users (id),
    FOREIGN KEY (subscription_id) REFERENCES subscriptions (id),
    FOREIGN KEY (group_id) REFERENCES xui_panel_groups (id)
);
```

## 📊 按分组查询流量统计

### 1. 获取特定分组的流量统计
```python
def get_group_traffic_stats(self, group_id: int, days: int = 30) -> Dict:
    # 查询指定分组的流量汇总
    total_stats = db.session.query(
        func.sum(TrafficStats.upload_bytes).label('total_upload'),
        func.sum(TrafficStats.download_bytes).label('total_download'),
        func.sum(TrafficStats.total_bytes).label('total_traffic'),
        func.count(TrafficStats.id).label('record_count')
    ).filter(
        TrafficStats.group_id == group_id,  # 🔑 按分组过滤
        TrafficStats.recorded_at >= start_date
    ).first()
```

### 2. 获取所有分组的流量汇总
```python
def get_all_groups_traffic_summary(self, days: int = 30) -> List[Dict]:
    # 按分组聚合流量统计
    group_stats = db.session.query(
        TrafficStats.group_id,
        XUIPanelGroup.name,
        func.sum(TrafficStats.total_bytes).label('total_traffic'),
        func.count(func.distinct(TrafficStats.user_id)).label('user_count')
    ).join(XUIPanelGroup, TrafficStats.group_id == XUIPanelGroup.id).filter(
        TrafficStats.recorded_at >= start_date
    ).group_by(TrafficStats.group_id)  # 🔑 按分组分组
```

## 🔍 数据查询示例

### 查看分组流量分布
```sql
SELECT 
    g.name AS group_name,
    COUNT(DISTINCT t.user_id) AS user_count,
    SUM(t.total_bytes) / (1024*1024*1024) AS total_gb,
    AVG(t.total_bytes) / (1024*1024) AS avg_mb_per_record
FROM traffic_stats t
JOIN xui_panel_groups g ON t.group_id = g.id
WHERE t.recorded_at >= datetime('now', '-30 days')
GROUP BY t.group_id, g.name
ORDER BY total_gb DESC;
```

### 查看用户在各分组的流量使用
```sql
SELECT 
    u.username,
    g.name AS group_name,
    SUM(t.total_bytes) / (1024*1024) AS total_mb
FROM traffic_stats t
JOIN users u ON t.user_id = u.id
LEFT JOIN xui_panel_groups g ON t.group_id = g.id
WHERE t.recorded_at >= datetime('now', '-7 days')
GROUP BY t.user_id, t.group_id
ORDER BY u.username, total_mb DESC;
```

## 💡 优化要点

### 1. **内存效率**
- 每个分组只缓存一次流量数据
- 多个订阅共享同一份缓存数据
- 处理完成后立即释放内存

### 2. **数据一致性**
- 同一客户端在多面板的流量自动合并
- 避免重复计算和数据冲突
- 事务性提交保证数据完整性

### 3. **查询性能**
- `group_id` 字段建立索引
- 按分组聚合查询效率高
- 支持时间范围过滤

### 4. **扩展性**
- 支持新增分组无需修改代码
- 兼容无分组的历史数据
- 灵活的统计维度组合
