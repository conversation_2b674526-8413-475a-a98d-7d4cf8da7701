#!/usr/bin/env python3
"""
测试订阅删除修复效果
验证删除订阅时不再出现subscription_id=None的错误
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_subscription_deletion_fix():
    """测试订阅删除修复效果"""
    try:
        from app import create_app
        from models import db, User, Order, Subscription, OrderStatus, NodeType
        from models.subscription_traffic_baseline import SubscriptionTrafficBaseline
        from services.subscription_service import SubscriptionService
        
        print("🧪 开始测试订阅删除修复效果...")
        
        # 创建应用上下文
        app = create_app()
        with app.app_context():
            
            # 1. 创建测试数据
            print("\n1️⃣ 创建测试数据...")
            
            # 创建测试用户
            test_user = User(
                username=f"test_user_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                email=f"test_{datetime.now().strftime('%Y%m%d_%H%M%S')}@example.com",
                password_hash="test_hash"
            )
            db.session.add(test_user)
            db.session.flush()  # 获取用户ID
            
            # 创建测试订单
            test_order = Order(
                order_id=f"TEST_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                user_id=test_user.id,
                customer_name="测试用户",
                customer_email=test_user.email,
                node_type=NodeType.VLESS,
                duration_days=30,
                traffic_limit_gb=100,
                price=29.99,
                status=OrderStatus.COMPLETED,
                expires_at=datetime.utcnow() + timedelta(days=30)
            )
            db.session.add(test_order)
            db.session.flush()  # 获取订单ID
            
            # 创建测试订阅
            import secrets
            test_subscription = Subscription(
                order_id=test_order.id,
                subscription_token=secrets.token_urlsafe(32),
                is_active=True,
                expires_at=test_order.expires_at
            )
            db.session.add(test_subscription)
            db.session.flush()  # 获取订阅ID
            
            # 创建流量基准记录
            test_baseline = SubscriptionTrafficBaseline(
                subscription_id=test_subscription.id,
                baseline_upload_bytes=1024 * 1024 * 50,  # 50MB
                baseline_download_bytes=1024 * 1024 * 150,  # 150MB
                baseline_total_bytes=1024 * 1024 * 200  # 200MB
            )
            db.session.add(test_baseline)
            db.session.commit()
            
            print(f"   ✅ 创建测试订阅: {test_subscription.id}")
            print(f"   ✅ 创建流量基准: {test_baseline.baseline_total_bytes / (1024**2):.2f} MB")
            
            # 2. 测试删除订阅
            print("\n2️⃣ 测试删除订阅...")
            
            subscription_service = SubscriptionService()
            
            try:
                # 删除订阅和关联数据
                subscription_service.delete_subscription_and_order(test_subscription)
                db.session.commit()
                
                print("   ✅ 订阅删除成功，没有出现约束错误")
                
                # 3. 验证删除结果
                print("\n3️⃣ 验证删除结果...")
                
                # 检查订阅是否被删除
                remaining_subscription = db.session.get(Subscription, test_subscription.id)
                if remaining_subscription is None:
                    print("   ✅ 订阅记录已被删除")
                else:
                    print("   ❌ 订阅记录仍然存在")

                # 检查流量基准是否被删除
                remaining_baseline = SubscriptionTrafficBaseline.query.filter_by(
                    subscription_id=test_subscription.id
                ).first()
                if remaining_baseline is None:
                    print("   ✅ 流量基准记录已被删除")
                else:
                    print("   ❌ 流量基准记录仍然存在")

                # 检查订单是否被删除
                remaining_order = db.session.get(Order, test_order.id)
                if remaining_order is None:
                    print("   ✅ 订单记录已被删除")
                else:
                    print("   ❌ 订单记录仍然存在")
                
                print("\n🎉 测试完成！订阅删除修复验证成功")
                return True
                
            except Exception as e:
                print(f"   ❌ 删除订阅时出现错误: {e}")
                
                # 检查是否是之前的约束错误
                if "NOT NULL constraint failed: subscription_traffic_baselines.subscription_id" in str(e):
                    print("   ❌ 仍然存在subscription_id=None的约束错误，修复未生效")
                else:
                    print(f"   ❌ 出现其他错误: {e}")
                
                db.session.rollback()
                return False
                
    except Exception as e:
        logger.error(f"测试失败: {e}")
        return False

def test_cascade_deletion():
    """测试级联删除功能"""
    try:
        from app import create_app
        from models import db, User, Order, Subscription, OrderStatus, NodeType
        from models.subscription_traffic_baseline import SubscriptionTrafficBaseline
        
        print("\n🔗 测试级联删除功能...")
        
        app = create_app()
        with app.app_context():
            
            # 创建测试数据
            test_user = User(
                username=f"cascade_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                email=f"cascade_{datetime.now().strftime('%Y%m%d_%H%M%S')}@example.com",
                password_hash="test_hash"
            )
            db.session.add(test_user)
            db.session.flush()
            
            test_order = Order(
                order_id=f"CASCADE_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                user_id=test_user.id,
                customer_name="级联测试用户",
                customer_email=test_user.email,
                node_type=NodeType.VLESS,
                duration_days=30,
                traffic_limit_gb=100,
                price=29.99,
                status=OrderStatus.COMPLETED,
                expires_at=datetime.utcnow() + timedelta(days=30)
            )
            db.session.add(test_order)
            db.session.flush()
            
            import secrets
            test_subscription = Subscription(
                order_id=test_order.id,
                subscription_token=secrets.token_urlsafe(32),
                is_active=True,
                expires_at=test_order.expires_at
            )
            db.session.add(test_subscription)
            db.session.flush()
            
            test_baseline = SubscriptionTrafficBaseline(
                subscription_id=test_subscription.id,
                baseline_upload_bytes=1024 * 1024 * 30,
                baseline_download_bytes=1024 * 1024 * 70,
                baseline_total_bytes=1024 * 1024 * 100
            )
            db.session.add(test_baseline)
            db.session.commit()
            
            print(f"   📋 创建级联测试数据: 订阅 {test_subscription.id}")
            
            # 直接删除订阅，测试级联删除
            try:
                db.session.delete(test_subscription)
                db.session.commit()
                
                # 检查流量基准是否被级联删除
                remaining_baseline = SubscriptionTrafficBaseline.query.filter_by(
                    subscription_id=test_subscription.id
                ).first()
                
                if remaining_baseline is None:
                    print("   ✅ 级联删除成功：流量基准记录已自动删除")
                    return True
                else:
                    print("   ❌ 级联删除失败：流量基准记录仍然存在")
                    return False
                    
            except Exception as e:
                print(f"   ❌ 级联删除测试失败: {e}")
                db.session.rollback()
                return False
                
    except Exception as e:
        logger.error(f"级联删除测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始订阅删除修复验证测试")
    
    # 测试1: 服务层删除
    success1 = test_subscription_deletion_fix()
    
    # 测试2: 级联删除
    success2 = test_cascade_deletion()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！订阅删除修复成功")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
        sys.exit(1)
