#!/usr/bin/env python3
"""
修复用户角色问题
"""
import sqlite3
import sys

def fix_user_roles():
    """修复数据库中的用户角色值"""
    try:
        conn = sqlite3.connect('instance/node_sales.db')
        cursor = conn.cursor()
        
        # 查看需要修复的用户
        cursor.execute('SELECT id, username, role FROM users WHERE role = "admin"')
        problem_users = cursor.fetchall()
        
        if not problem_users:
            print("没有发现需要修复的用户角色")
            return
            
        print(f"发现 {len(problem_users)} 个需要修复的用户:")
        for user in problem_users:
            print(f"  ID: {user[0]}, 用户名: {user[1]}, 当前角色: {user[2]}")
        
        # 自动执行修复
        print("\n开始修复用户角色...")
            
        # 执行修复
        cursor.execute('UPDATE users SET role = "ADMIN" WHERE role = "admin"')
        affected_rows = cursor.rowcount
        
        conn.commit()
        print(f"成功修复了 {affected_rows} 个用户的角色")
        
        # 验证修复结果
        cursor.execute('SELECT id, username, role FROM users WHERE id IN ({})'.format(
            ','.join(str(user[0]) for user in problem_users)
        ))
        fixed_users = cursor.fetchall()
        
        print("\n修复后的用户:")
        for user in fixed_users:
            print(f"  ID: {user[0]}, 用户名: {user[1]}, 角色: {user[2]}")
            
        # 检查是否还有其他问题
        cursor.execute('SELECT DISTINCT role FROM users')
        all_roles = cursor.fetchall()
        print(f"\n数据库中所有角色值: {[role[0] for role in all_roles]}")
        
        conn.close()
        print("\n修复完成！")
        
    except Exception as e:
        print(f"修复失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    fix_user_roles()
