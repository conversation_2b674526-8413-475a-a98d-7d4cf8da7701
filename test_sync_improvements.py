#!/usr/bin/env python3
"""
测试同步功能改进
验证：
1. 日志功能已删除
2. 客户端重复检测功能
3. 从分组移除面板时的客户端清理功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.subscription_sync_service import SubscriptionSyncService
from xui_client import XUIClient

def test_sync_service_no_logs():
    """测试同步服务不再有日志功能"""
    print("=== 测试同步服务日志功能删除 ===")
    
    sync_service = SubscriptionSyncService()
    
    # 检查是否还有日志方法
    has_create_log = hasattr(sync_service, '_create_sync_log')
    has_update_log = hasattr(sync_service, '_update_sync_log')
    
    print(f"是否还有 _create_sync_log 方法: {has_create_log}")
    print(f"是否还有 _update_sync_log 方法: {has_update_log}")
    
    if not has_create_log and not has_update_log:
        print("✅ 日志功能已成功删除")
    else:
        print("❌ 日志功能未完全删除")
    
    return not has_create_log and not has_update_log

def test_client_exists_method():
    """测试客户端存在检查方法"""
    print("\n=== 测试客户端重复检测功能 ===")
    
    # 检查XUIClient是否有新的方法
    has_client_exists = hasattr(XUIClient, 'client_exists_by_email')
    
    print(f"XUIClient 是否有 client_exists_by_email 方法: {has_client_exists}")
    
    if has_client_exists:
        print("✅ 客户端重复检测方法已添加")
        
        # 检查方法签名
        import inspect
        sig = inspect.signature(XUIClient.client_exists_by_email)
        print(f"方法签名: {sig}")
        
        return True
    else:
        print("❌ 客户端重复检测方法未添加")
        return False

def test_sync_method_signatures():
    """测试同步方法的签名是否正确"""
    print("\n=== 测试同步方法签名 ===")
    
    sync_service = SubscriptionSyncService()
    
    # 检查方法签名
    import inspect
    
    # 检查 sync_group_subscriptions_for_new_panel 方法
    if hasattr(sync_service, 'sync_group_subscriptions_for_new_panel'):
        sig1 = inspect.signature(sync_service.sync_group_subscriptions_for_new_panel)
        print(f"sync_group_subscriptions_for_new_panel 签名: {sig1}")
        
        # 应该只有 group_id 和 panel_id 参数（不包括self）
        params = list(sig1.parameters.keys())
        expected_params = ['group_id', 'panel_id']

        if params == expected_params:
            print("✅ sync_group_subscriptions_for_new_panel 签名正确")
        else:
            print(f"❌ sync_group_subscriptions_for_new_panel 签名不正确，期望: {expected_params}, 实际: {params}")
    
    # 检查 sync_group_all_panels 方法
    if hasattr(sync_service, 'sync_group_all_panels'):
        sig2 = inspect.signature(sync_service.sync_group_all_panels)
        print(f"sync_group_all_panels 签名: {sig2}")
        
        # 应该只有 group_id 参数（不包括self）
        params = list(sig2.parameters.keys())
        expected_params = ['group_id']

        if params == expected_params:
            print("✅ sync_group_all_panels 签名正确")
            return True
        else:
            print(f"❌ sync_group_all_panels 签名不正确，期望: {expected_params}, 实际: {params}")
            return False
    
    return False

def test_admin_route_changes():
    """测试管理员路由的修改"""
    print("\n=== 测试管理员路由修改 ===")
    
    # 检查 routes/admin.py 文件内容
    try:
        with open('routes/admin.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否包含客户端清理逻辑
        has_client_cleanup = 'deleted_clients' in content and 'failed_deletions' in content
        has_xui_client_import = 'from xui_client import XUIClient' in content
        has_delete_client_call = 'delete_client_by_email' in content
        
        print(f"是否包含客户端清理逻辑: {has_client_cleanup}")
        print(f"是否导入XUIClient: {has_xui_client_import}")
        print(f"是否调用删除客户端方法: {has_delete_client_call}")
        
        if has_client_cleanup and has_xui_client_import and has_delete_client_call:
            print("✅ 管理员路由已正确修改，包含客户端清理功能")
            return True
        else:
            print("❌ 管理员路由修改不完整")
            return False
            
    except Exception as e:
        print(f"❌ 读取管理员路由文件失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试同步功能改进...")
    
    results = []
    
    # 运行所有测试
    results.append(test_sync_service_no_logs())
    results.append(test_client_exists_method())
    results.append(test_sync_method_signatures())
    results.append(test_admin_route_changes())
    
    # 汇总结果
    print("\n" + "="*50)
    print("测试结果汇总:")
    print(f"通过的测试: {sum(results)}/{len(results)}")
    
    if all(results):
        print("🎉 所有测试通过！同步功能改进成功完成。")
        print("\n主要改进:")
        print("1. ✅ 删除了同步功能的日志记录")
        print("2. ✅ 添加了客户端重复检测机制")
        print("3. ✅ 增强了从分组移除面板时的客户端清理功能")
    else:
        print("❌ 部分测试失败，需要进一步检查。")
    
    return all(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
