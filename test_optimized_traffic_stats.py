#!/usr/bin/env python3
"""
测试优化后的流量统计收集功能
"""
import sys
import os
import logging
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import db, User, Subscription, Order, OrderStatus, XUIPanelGroup, TrafficStats
from services.traffic_stats_service import TrafficStatsService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_optimized_traffic_collection():
    """测试优化后的流量统计收集"""
    app = create_app()
    
    with app.app_context():
        try:
            logger.info("开始测试优化后的流量统计收集...")
            
            # 检查数据库中的活跃订阅 - 明确指定连接条件以避免多外键关系的歧义
            active_subscriptions = Subscription.query.join(
                Order, Subscription.order_id == Order.id
            ).filter(
                Subscription.is_active == True,
                Subscription.expires_at > datetime.utcnow(),
                Order.status == OrderStatus.COMPLETED
            ).all()
            
            logger.info(f"找到 {len(active_subscriptions)} 个活跃订阅")
            
            # 按分组统计订阅分布
            from collections import defaultdict
            group_distribution = defaultdict(int)
            for sub in active_subscriptions:
                group_id = sub.group_id or "默认分组"
                group_distribution[group_id] += 1
            
            logger.info("订阅分组分布:")
            for group_id, count in group_distribution.items():
                if group_id == "默认分组":
                    logger.info(f"  默认分组: {count} 个订阅")
                else:
                    group = XUIPanelGroup.query.get(group_id)
                    group_name = group.name if group else f"分组{group_id}"
                    logger.info(f"  {group_name} (ID: {group_id}): {count} 个订阅")
            
            # 创建流量统计服务实例
            traffic_service = TrafficStatsService()
            
            # 记录开始时间
            start_time = datetime.now()
            logger.info(f"开始执行流量统计收集: {start_time}")
            
            # 执行优化后的流量统计收集
            success = traffic_service.collect_all_traffic_stats()
            
            # 记录结束时间
            end_time = datetime.now()
            duration = end_time - start_time
            logger.info(f"流量统计收集完成: {end_time}")
            logger.info(f"总耗时: {duration.total_seconds():.2f} 秒")
            
            if success:
                logger.info("✅ 流量统计收集成功")
                
                # 检查最新的流量统计记录
                recent_stats = TrafficStats.query.filter(
                    TrafficStats.recorded_at >= start_time
                ).all()
                
                logger.info(f"本次收集生成了 {len(recent_stats)} 条流量统计记录")
                
                # 按分组统计新记录
                group_stats = defaultdict(list)
                for stat in recent_stats:
                    group_id = stat.group_id or "默认分组"
                    group_stats[group_id].append(stat)
                
                logger.info("新记录按分组分布:")
                for group_id, stats in group_stats.items():
                    if group_id == "默认分组":
                        logger.info(f"  默认分组: {len(stats)} 条记录")
                    else:
                        group = XUIPanelGroup.query.get(group_id)
                        group_name = group.name if group else f"分组{group_id}"
                        logger.info(f"  {group_name} (ID: {group_id}): {len(stats)} 条记录")
                        
                        # 显示该分组的流量汇总
                        total_traffic = sum(stat.total_bytes for stat in stats)
                        total_mb = total_traffic / (1024 * 1024)
                        logger.info(f"    总流量: {total_mb:.2f} MB")
                
            else:
                logger.error("❌ 流量统计收集失败")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"测试过程中发生错误: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return False

def test_performance_comparison():
    """测试性能对比（模拟）"""
    app = create_app()
    
    with app.app_context():
        try:
            logger.info("开始性能对比测试...")
            
            # 获取活跃订阅数量 - 明确指定连接条件以避免多外键关系的歧义
            subscription_count = Subscription.query.join(
                Order, Subscription.order_id == Order.id
            ).filter(
                Subscription.is_active == True,
                Subscription.expires_at > datetime.utcnow(),
                Order.status == OrderStatus.COMPLETED
            ).count()
            
            # 获取分组数量
            group_count = XUIPanelGroup.query.filter(
                XUIPanelGroup.is_active == True
            ).count()
            
            # 获取面板数量
            from models import XUIPanel, PanelStatus
            panel_count = XUIPanel.query.filter(
                XUIPanel.status == PanelStatus.ACTIVE
            ).count()
            
            logger.info(f"系统统计:")
            logger.info(f"  活跃订阅数: {subscription_count}")
            logger.info(f"  活跃分组数: {group_count}")
            logger.info(f"  活跃面板数: {panel_count}")
            
            # 估算API调用次数
            old_api_calls = subscription_count * panel_count  # 旧方法：每个订阅访问所有面板
            new_api_calls = group_count * panel_count  # 新方法：每个分组访问一次面板
            
            logger.info(f"API调用次数对比:")
            logger.info(f"  旧方法 (逐个订阅): {old_api_calls} 次")
            logger.info(f"  新方法 (按分组批量): {new_api_calls} 次")
            
            if old_api_calls > 0:
                reduction_ratio = (old_api_calls - new_api_calls) / old_api_calls * 100
                logger.info(f"  优化效果: 减少 {reduction_ratio:.1f}% 的API调用")
            
            return True
            
        except Exception as e:
            logger.error(f"性能对比测试失败: {e}")
            return False

if __name__ == "__main__":
    logger.info("=" * 60)
    logger.info("流量统计优化测试")
    logger.info("=" * 60)
    
    # 测试优化后的流量收集
    if test_optimized_traffic_collection():
        logger.info("✅ 优化后的流量收集测试通过")
    else:
        logger.error("❌ 优化后的流量收集测试失败")
        sys.exit(1)
    
    logger.info("-" * 60)
    
    # 测试性能对比
    if test_performance_comparison():
        logger.info("✅ 性能对比测试通过")
    else:
        logger.error("❌ 性能对比测试失败")
        sys.exit(1)
    
    logger.info("=" * 60)
    logger.info("所有测试完成")
    logger.info("=" * 60)
