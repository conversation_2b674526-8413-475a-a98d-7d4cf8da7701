#!/usr/bin/env python3
"""
测试优惠券功能
"""
from app import create_app
from models import db, Coupon

def test_coupons():
    app = create_app()
    with app.app_context():
        try:
            # 查看所有优惠券
            coupons = Coupon.query.all()
            print(f"总共有 {len(coupons)} 个优惠券:")
            
            for coupon in coupons:
                print(f"  ID: {coupon.id}")
                print(f"  代码: {coupon.code}")
                print(f"  折扣: {coupon.discount_percentage}%")
                print(f"  最大使用次数: {coupon.max_uses}")
                print(f"  已使用次数: {coupon.used_count}")
                print(f"  是否可用: {coupon.is_available()}")
                print(f"  剩余次数: {coupon.get_remaining_uses()}")
                print(f"  是否激活: {coupon.is_active}")
                print("  ---")
                
        except Exception as e:
            print(f"错误: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_coupons()
