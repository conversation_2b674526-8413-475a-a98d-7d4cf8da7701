#!/usr/bin/env python3
"""
测试同步功能修复
验证方法调用参数是否正确
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import inspect
from services.subscription_sync_service import SubscriptionSyncService

def test_method_signatures():
    """测试方法签名是否匹配"""
    print("=== 测试方法签名匹配 ===")
    
    sync_service = SubscriptionSyncService()
    
    # 检查 _add_panel_node_for_subscription 方法签名
    sig = inspect.signature(sync_service._add_panel_node_for_subscription)
    params = list(sig.parameters.keys())
    
    print(f"_add_panel_node_for_subscription 参数: {params}")
    
    # 应该只有 subscription 和 panel 参数
    expected_params = ['subscription', 'panel']
    
    if params == expected_params:
        print("✅ _add_panel_node_for_subscription 方法签名正确")
        return True
    else:
        print(f"❌ _add_panel_node_for_subscription 方法签名不正确")
        print(f"期望: {expected_params}")
        print(f"实际: {params}")
        return False

def test_sync_group_all_panels_code():
    """检查 sync_group_all_panels 方法中的调用是否正确"""
    print("\n=== 检查方法调用代码 ===")
    
    try:
        with open('services/subscription_sync_service.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找 sync_group_all_panels 方法中的调用
        lines = content.split('\n')
        in_sync_group_all_panels = False
        call_found = False
        correct_call = False
        
        for i, line in enumerate(lines):
            if 'def sync_group_all_panels' in line:
                in_sync_group_all_panels = True
                continue
            
            if in_sync_group_all_panels and line.strip().startswith('def '):
                # 进入下一个方法，停止检查
                break
            
            if in_sync_group_all_panels and '_add_panel_node_for_subscription(' in line:
                call_found = True
                print(f"找到调用 (第{i+1}行): {line.strip()}")

                # 检查下一行是否包含正确的参数
                if i+1 < len(lines):
                    next_line = lines[i+1].strip()
                    print(f"下一行 (第{i+2}行): {next_line}")

                    if 'subscription, panel' in next_line and 'None' not in next_line:
                        correct_call = True
                        print("✅ 调用参数正确")
                    else:
                        print("❌ 调用参数不正确")
                else:
                    print("❌ 无法检查参数")
        
        if not call_found:
            print("❌ 未找到方法调用")
            return False
        
        return correct_call
        
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试同步功能修复...")
    
    results = []
    
    # 运行测试
    results.append(test_method_signatures())
    results.append(test_sync_group_all_panels_code())
    
    # 汇总结果
    print("\n" + "="*50)
    print("测试结果汇总:")
    print(f"通过的测试: {sum(results)}/{len(results)}")
    
    if all(results):
        print("🎉 所有测试通过！同步功能修复成功。")
    else:
        print("❌ 部分测试失败，需要进一步检查。")
    
    return all(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
