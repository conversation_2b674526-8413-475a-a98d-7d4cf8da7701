#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调度器功能和用户统计中心流量统计测试脚本
测试内容：
1. 调度器流量耗尽订阅清理功能
2. 用户统计中心流量统计显示功能
3. 流量统计数据库查询功能
"""

import os
import sys
import logging
from datetime import datetime, timezone
from flask import Flask

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import db, User, Order, Subscription, TrafficStats
from services.scheduler_service import scheduler_service
from services.traffic_stats_service import TrafficStatsService
from services.subscription_service import SubscriptionService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SchedulerAndTrafficStatsTest:
    """调度器和流量统计测试类"""
    
    def __init__(self):
        self.app = create_app()
        self.traffic_stats_service = TrafficStatsService()
        self.subscription_service = SubscriptionService()
        
    def setup_test_data(self):
        """设置测试数据"""
        logger.info("=== 设置测试数据 ===")
        
        with self.app.app_context():
            try:
                # 查找现有用户或创建测试用户
                test_user = User.query.filter_by(email='<EMAIL>').first()
                if not test_user:
                    # 检查用户名是否已存在
                    existing_user = User.query.filter_by(username='testuser').first()
                    if existing_user:
                        # 如果用户名存在但邮箱不同，使用时间戳创建唯一用户名
                        import time
                        username = f'testuser_{int(time.time())}'
                    else:
                        username = 'testuser'

                    test_user = User(
                        username=username,
                        email='<EMAIL>',
                        password_hash='test_hash',
                        is_active=True
                    )
                    db.session.add(test_user)
                    db.session.commit()
                    logger.info(f"创建测试用户: {test_user.id} (用户名: {username})")
                else:
                    logger.info(f"使用现有测试用户: {test_user.id}")
                
                # 查找现有订单或创建测试订单
                test_order = Order.query.filter_by(customer_email='<EMAIL>').first()
                if not test_order:
                    test_order = Order(
                        order_id=f'TEST_{datetime.now().strftime("%Y%m%d_%H%M%S")}',
                        customer_email='<EMAIL>',
                        product_name='测试套餐',
                        price=99.99,
                        traffic_limit_gb=10,  # 10GB流量限制
                        duration_days=30,
                        status='completed',
                        user_id=test_user.id
                    )
                    db.session.add(test_order)
                    db.session.commit()
                    logger.info(f"创建测试订单: {test_order.order_id}")
                else:
                    # 确保现有订单有正确的user_id和状态
                    updated = False
                    if test_order.user_id is None:
                        test_order.user_id = test_user.id
                        updated = True
                        logger.info(f"更新现有测试订单的用户ID: {test_order.order_id}")

                    # 确保订单状态为completed，这样订阅服务才能返回它
                    from models.enums import OrderStatus
                    if test_order.status != OrderStatus.COMPLETED:
                        test_order.status = OrderStatus.COMPLETED
                        updated = True
                        logger.info(f"更新现有测试订单状态为COMPLETED: {test_order.order_id}")

                    if updated:
                        db.session.commit()
                    else:
                        logger.info(f"使用现有测试订单: {test_order.order_id}")
                
                # 查找现有订阅或创建测试订阅
                test_subscription = Subscription.query.filter_by(order_id=test_order.id).first()
                if not test_subscription:
                    # 生成订阅令牌
                    import uuid
                    subscription_token = str(uuid.uuid4())

                    test_subscription = Subscription(
                        order_id=test_order.id,
                        subscription_token=subscription_token,
                        group_id=None,  # 默认分组
                        is_active=True
                    )
                    db.session.add(test_subscription)
                    db.session.commit()
                    logger.info(f"创建测试订阅: {test_subscription.id}")
                else:
                    logger.info(f"使用现有测试订阅: {test_subscription.id}")
                
                # 创建流量统计数据（模拟流量耗尽情况）
                # 删除旧的流量统计记录
                TrafficStats.query.filter_by(subscription_id=test_subscription.id).delete()
                
                # 创建流量耗尽的统计记录（超过10GB限制）
                exhausted_traffic = TrafficStats(
                    user_id=test_order.user_id,  # 从订单获取用户ID
                    subscription_id=test_subscription.id,
                    group_id=test_subscription.group_id,
                    upload_bytes=5 * 1024 * 1024 * 1024,  # 5GB上传
                    download_bytes=6 * 1024 * 1024 * 1024,  # 6GB下载
                    total_bytes=11 * 1024 * 1024 * 1024,  # 总计11GB（超过10GB限制）
                    recorded_at=datetime.utcnow()
                )
                db.session.add(exhausted_traffic)
                db.session.commit()
                
                logger.info(f"创建流量耗尽统计记录: 总流量 {exhausted_traffic.total_bytes / (1024**3):.2f}GB")
                
                return test_user, test_order, test_subscription
                
            except Exception as e:
                logger.error(f"设置测试数据失败: {e}")
                db.session.rollback()
                raise
    
    def test_traffic_stats_database_query(self):
        """测试流量统计数据库查询功能"""
        logger.info("=== 测试流量统计数据库查询功能 ===")
        
        with self.app.app_context():
            try:
                # 获取测试订阅
                test_subscription = Subscription.query.filter_by(is_active=True).first()
                if not test_subscription:
                    logger.error("没有找到活跃的测试订阅")
                    return False
                
                test_order = test_subscription.order
                if not test_order:
                    logger.error("订阅没有关联的订单")
                    return False
                
                logger.info(f"测试订阅ID: {test_subscription.id}, 订单: {test_order.order_id}")
                
                # 测试从数据库获取流量统计
                traffic_stats = self.traffic_stats_service.get_subscription_latest_traffic_stats(
                    test_subscription.id, test_order
                )
                
                logger.info("流量统计查询结果:")
                logger.info(f"  - 上传流量: {traffic_stats['total_up_mb']} MB")
                logger.info(f"  - 下载流量: {traffic_stats['total_down_mb']} MB")
                logger.info(f"  - 总流量: {traffic_stats['total_traffic_mb']} MB")
                logger.info(f"  - 流量限制: {traffic_stats['traffic_limit_mb']} MB")
                logger.info(f"  - 使用百分比: {traffic_stats['usage_percentage']}%")
                logger.info(f"  - 剩余流量: {traffic_stats['remaining_mb']} MB")
                logger.info(f"  - 最后更新: {traffic_stats['last_updated']}")
                
                # 验证数据正确性
                expected_limit_mb = test_order.traffic_limit_gb * 1024
                if traffic_stats['traffic_limit_mb'] != expected_limit_mb:
                    logger.error(f"流量限制不正确: 期望 {expected_limit_mb}, 实际 {traffic_stats['traffic_limit_mb']}")
                    return False
                
                if traffic_stats['remaining_mb'] <= 0:
                    logger.info("✓ 检测到流量耗尽情况")
                else:
                    logger.warning("流量未耗尽，可能影响调度器测试")
                
                return True
                
            except Exception as e:
                logger.error(f"流量统计数据库查询测试失败: {e}")
                return False
    
    def test_user_center_traffic_display(self):
        """测试用户中心流量显示功能"""
        logger.info("=== 测试用户中心流量显示功能 ===")

        with self.app.app_context():
            try:
                # 获取测试用户
                test_user = User.query.filter_by(email='<EMAIL>').first()
                if not test_user:
                    logger.error("没有找到测试用户")
                    return False

                logger.info(f"测试用户ID: {test_user.id}")

                # 直接查询数据库中的订阅和订单
                from models import Order, Subscription
                user_orders = Order.query.filter_by(user_id=test_user.id).all()
                logger.info(f"用户订单数量: {len(user_orders)}")

                for order in user_orders:
                    logger.info(f"订单: {order.order_id}, 状态: {order.status}")
                    subscription = Subscription.query.filter_by(order_id=order.id).first()
                    if subscription:
                        logger.info(f"  - 关联订阅ID: {subscription.id}, 活跃: {subscription.is_active}")
                    else:
                        logger.info(f"  - 没有关联的订阅")

                # 获取用户订阅（模拟用户中心逻辑）
                subscriptions = self.subscription_service.get_user_subscriptions(test_user.id)

                logger.info(f"用户订阅数量: {len(subscriptions)}")

                if len(subscriptions) == 0:
                    logger.warning("用户没有订阅，可能是订阅服务逻辑问题")
                    # 尝试直接测试订阅服务的内部逻辑
                    logger.info("尝试直接查询用户订单...")
                    orders = Order.query.filter_by(user_id=test_user.id).all()
                    for order in orders:
                        logger.info(f"处理订单: {order.order_id}")
                        try:
                            # 测试获取订单流量统计
                            traffic_stats = self.subscription_service._get_order_traffic_stats(order)
                            logger.info(f"订单流量统计: {traffic_stats}")
                        except Exception as e:
                            logger.error(f"获取订单流量统计失败: {e}")

                    return False

                # 计算总流量统计（模拟用户中心逻辑）
                total_traffic_mb = 0
                total_limit_mb = 0
                total_remaining_mb = 0
                active_count = 0

                for subscription in subscriptions:
                    logger.info(f"订阅: {subscription['product_name']}")
                    logger.info(f"  - 状态: {'活跃' if subscription['is_active'] else '非活跃'}")
                    logger.info(f"  - 过期: {'是' if subscription['is_expired'] else '否'}")

                    if subscription['is_active']:
                        active_count += 1
                        stats = subscription['traffic_stats']
                        total_traffic_mb += stats['total_traffic_mb']
                        total_limit_mb += stats['traffic_limit_mb']
                        total_remaining_mb += stats['remaining_mb']

                        logger.info(f"  - 流量统计:")
                        logger.info(f"    * 已用: {stats['total_traffic_mb']} MB")
                        logger.info(f"    * 限制: {stats['traffic_limit_mb']} MB")
                        logger.info(f"    * 剩余: {stats['remaining_mb']} MB")
                        logger.info(f"    * 使用率: {stats['usage_percentage']}%")

                # 汇总统计
                usage_percentage = (total_traffic_mb / total_limit_mb * 100) if total_limit_mb > 0 else 0

                logger.info("用户中心汇总统计:")
                logger.info(f"  - 活跃订阅: {active_count}")
                logger.info(f"  - 总已用流量: {round(total_traffic_mb, 2)} MB")
                logger.info(f"  - 总流量限制: {round(total_limit_mb, 2)} MB")
                logger.info(f"  - 总剩余流量: {round(total_remaining_mb, 2)} MB")
                logger.info(f"  - 总使用率: {round(usage_percentage, 1)}%")

                return len(subscriptions) > 0

            except Exception as e:
                logger.error(f"用户中心流量显示测试失败: {e}")
                return False
    
    def test_scheduler_traffic_exhausted_cleanup(self):
        """测试调度器流量耗尽订阅清理功能"""
        logger.info("=== 测试调度器流量耗尽订阅清理功能 ===")
        
        try:
            # 初始化调度器
            scheduler_service.init_app(self.app)
            
            # 手动触发流量耗尽订阅清理
            logger.info("手动触发流量耗尽订阅清理...")
            result = scheduler_service.trigger_traffic_exhausted_cleanup()
            
            if result['success']:
                stats = result['stats']
                logger.info("流量耗尽订阅清理结果:")
                logger.info(f"  - 开始时间: {stats['start_time']}")
                logger.info(f"  - 结束时间: {stats['end_time']}")
                logger.info(f"  - 执行时长: {stats['duration_seconds']} 秒")
                logger.info(f"  - 发现流量耗尽订阅: {stats['total_exhausted']}")
                logger.info(f"  - 成功删除: {stats['successfully_deleted']}")
                logger.info(f"  - 删除失败: {stats['failed_deletions']}")
                logger.info(f"  - X-UI删除失败: {stats['xui_deletion_failures']}")
                logger.info(f"  - 数据库删除失败: {stats['db_deletion_failures']}")
                
                if stats['errors']:
                    logger.warning("清理过程中的错误:")
                    for error in stats['errors']:
                        logger.warning(f"  - {error}")
                
                # 详细处理结果
                if stats['processed_subscriptions']:
                    logger.info("详细处理结果:")
                    for result in stats['processed_subscriptions']:
                        logger.info(f"  订阅 {result['subscription_id']} (订单 {result['order_number']}):")
                        logger.info(f"    - 处理成功: {result['success']}")
                        logger.info(f"    - X-UI删除成功: {result['xui_deletion_success']}")
                        logger.info(f"    - 数据库删除成功: {result['db_deletion_success']}")
                        if result['error']:
                            logger.info(f"    - 错误: {result['error']}")
                
                return True
            else:
                logger.error(f"流量耗尽订阅清理失败: {result['error']}")
                return False
                
        except Exception as e:
            logger.error(f"调度器流量耗尽清理测试失败: {e}")
            return False
    
    def test_scheduler_traffic_stats_collection(self):
        """测试调度器流量统计收集功能"""
        logger.info("=== 测试调度器流量统计收集功能 ===")
        
        try:
            # 初始化调度器
            scheduler_service.init_app(self.app)
            
            # 手动触发流量统计收集
            logger.info("手动触发流量统计收集...")
            success = scheduler_service.trigger_traffic_stats_collection()
            
            if success:
                logger.info("✓ 流量统计收集任务执行成功")
                return True
            else:
                logger.error("✗ 流量统计收集任务执行失败")
                return False
                
        except Exception as e:
            logger.error(f"调度器流量统计收集测试失败: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        logger.info("开始执行调度器和流量统计功能测试")
        logger.info("=" * 60)
        
        test_results = {}
        
        try:
            # 设置测试数据
            self.setup_test_data()
            
            # 测试流量统计数据库查询
            test_results['traffic_stats_db_query'] = self.test_traffic_stats_database_query()
            
            # 测试用户中心流量显示
            test_results['user_center_display'] = self.test_user_center_traffic_display()
            
            # 测试调度器流量统计收集
            test_results['scheduler_stats_collection'] = self.test_scheduler_traffic_stats_collection()
            
            # 测试调度器流量耗尽清理
            test_results['scheduler_exhausted_cleanup'] = self.test_scheduler_traffic_exhausted_cleanup()
            
        except Exception as e:
            logger.error(f"测试执行过程中发生错误: {e}")
            test_results['error'] = str(e)
        
        # 输出测试结果汇总
        logger.info("=" * 60)
        logger.info("测试结果汇总:")
        
        for test_name, result in test_results.items():
            if test_name == 'error':
                logger.error(f"执行错误: {result}")
            else:
                status = "✓ 通过" if result else "✗ 失败"
                logger.info(f"  {test_name}: {status}")
        
        # 计算通过率
        passed_tests = sum(1 for result in test_results.values() if result is True)
        total_tests = len([k for k in test_results.keys() if k != 'error'])
        pass_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        logger.info(f"测试通过率: {passed_tests}/{total_tests} ({pass_rate:.1f}%)")
        
        return test_results

def main():
    """主函数"""
    test_runner = SchedulerAndTrafficStatsTest()
    results = test_runner.run_all_tests()
    
    # 根据测试结果设置退出码
    if all(result is True for key, result in results.items() if key != 'error'):
        sys.exit(0)  # 所有测试通过
    else:
        sys.exit(1)  # 有测试失败

if __name__ == '__main__':
    main()
