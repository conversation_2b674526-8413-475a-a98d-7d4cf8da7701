#!/usr/bin/env python3
"""
简单的流量数据库集成测试
验证修改后的代码能正常工作
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试导入是否正常"""
    try:
        from services.traffic_stats_service import TrafficStatsService
        from services.subscription_service import SubscriptionService
        print("✅ 导入测试通过")
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_traffic_service_methods():
    """测试TrafficStatsService的新方法是否存在"""
    try:
        from services.traffic_stats_service import TrafficStatsService
        service = TrafficStatsService()
        
        # 检查新方法是否存在
        assert hasattr(service, 'get_subscription_latest_traffic_stats'), "缺少 get_subscription_latest_traffic_stats 方法"
        assert hasattr(service, 'get_order_traffic_stats_from_db'), "缺少 get_order_traffic_stats_from_db 方法"
        
        print("✅ TrafficStatsService 方法检查通过")
        return True
    except Exception as e:
        print(f"❌ TrafficStatsService 方法检查失败: {e}")
        return False

def test_subscription_service_modification():
    """测试SubscriptionService的修改是否正确"""
    try:
        from services.subscription_service import SubscriptionService
        service = SubscriptionService()
        
        # 检查_get_order_traffic_stats方法是否存在
        assert hasattr(service, '_get_order_traffic_stats'), "缺少 _get_order_traffic_stats 方法"
        
        print("✅ SubscriptionService 修改检查通过")
        return True
    except Exception as e:
        print(f"❌ SubscriptionService 修改检查失败: {e}")
        return False

def test_no_api_dependencies():
    """测试是否移除了不必要的API依赖"""
    try:
        from services.subscription_service import SubscriptionService
        import inspect
        
        # 获取_get_order_traffic_stats方法的源代码
        service = SubscriptionService()
        method = getattr(service, '_get_order_traffic_stats')
        source = inspect.getsource(method)
        
        # 检查是否不再包含API调用相关代码
        api_keywords = ['get_client_traffic_summary', 'MultiXUIManager', 'manager.get']
        found_api_calls = [keyword for keyword in api_keywords if keyword in source]
        
        if found_api_calls:
            print(f"⚠️  仍然包含API调用: {found_api_calls}")
        else:
            print("✅ API依赖移除检查通过")
        
        return len(found_api_calls) == 0
    except Exception as e:
        print(f"❌ API依赖检查失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("开始流量数据库集成测试...")
    print("=" * 50)
    
    tests = [
        ("导入测试", test_imports),
        ("TrafficStatsService方法测试", test_traffic_service_methods),
        ("SubscriptionService修改测试", test_subscription_service_modification),
        ("API依赖移除测试", test_no_api_dependencies),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n运行 {test_name}...")
        if test_func():
            passed += 1
        else:
            print(f"测试失败: {test_name}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！流量数据库集成修改成功。")
        return True
    else:
        print("❌ 部分测试失败，请检查修改。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
