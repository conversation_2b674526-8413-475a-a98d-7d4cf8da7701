#!/usr/bin/env python3
"""
测试订阅覆盖功能 - 验证renewal_tasks级联删除修复
"""
import sys
import os
import logging
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import db, User, Subscription, Order, OrderStatus, RenewalTask, RenewalTaskStatus
from services.subscription_service import SubscriptionService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_subscription_override():
    """测试订阅覆盖功能"""
    app = create_app()
    
    with app.app_context():
        try:
            logger.info("开始测试订阅覆盖功能...")
            
            # 查找一个有活跃订阅的用户 - 明确指定连接条件
            user = User.query.join(
                Order, User.id == Order.user_id
            ).join(
                Subscription, Order.id == Subscription.order_id
            ).filter(
                Subscription.is_active == True,
                Order.status == OrderStatus.COMPLETED
            ).first()
            
            if not user:
                logger.warning("没有找到有活跃订阅的用户，创建测试数据...")
                return False
            
            logger.info(f"找到测试用户: {user.username} (ID: {user.id})")
            
            # 获取用户当前的订阅
            current_subscriptions = Subscription.query.join(
                Order, Subscription.order_id == Order.id
            ).filter(
                Order.user_id == user.id,
                Subscription.is_active == True,
                Order.status == OrderStatus.COMPLETED
            ).all()
            
            logger.info(f"用户当前有 {len(current_subscriptions)} 个活跃订阅")
            
            # 为第一个订阅创建一些测试的续费任务记录
            if current_subscriptions:
                test_subscription = current_subscriptions[0]
                logger.info(f"为订阅 {test_subscription.id} 创建测试续费任务记录...")
                
                # 创建测试续费任务
                test_renewal_task = RenewalTask(
                    subscription_id=test_subscription.id,
                    renewal_months=3,
                    original_expiry=datetime.utcnow(),
                    new_expiry=datetime.utcnow() + timedelta(days=90),
                    status=RenewalTaskStatus.COMPLETED
                )
                
                db.session.add(test_renewal_task)
                db.session.commit()
                
                logger.info(f"创建了测试续费任务 {test_renewal_task.id}")
                
                # 验证续费任务记录存在
                renewal_tasks_count = RenewalTask.query.filter_by(subscription_id=test_subscription.id).count()
                logger.info(f"订阅 {test_subscription.id} 当前有 {renewal_tasks_count} 条续费任务记录")
            
            # 测试订阅覆盖功能
            logger.info("开始测试订阅覆盖...")
            subscription_service = SubscriptionService()
            
            # 模拟用户购买新订阅，这会触发旧订阅的删除
            result = subscription_service.handle_user_subscription_override(user.id)
            
            if result:
                logger.info("✅ 订阅覆盖处理成功")
                
                # 验证旧的续费任务记录是否被正确删除
                remaining_renewal_tasks = RenewalTask.query.filter_by(subscription_id=test_subscription.id).count()
                logger.info(f"覆盖后，原订阅 {test_subscription.id} 的续费任务记录数量: {remaining_renewal_tasks}")
                
                if remaining_renewal_tasks == 0:
                    logger.info("✅ 续费任务记录已正确删除")
                else:
                    logger.warning(f"⚠️ 仍有 {remaining_renewal_tasks} 条续费任务记录未删除")
                
                return True
            else:
                logger.error("❌ 订阅覆盖处理失败")
                return False
            
        except Exception as e:
            logger.error(f"测试过程中发生错误: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return False

def test_manual_deletion():
    """测试手动删除订阅功能"""
    app = create_app()
    
    with app.app_context():
        try:
            logger.info("开始测试手动删除订阅功能...")
            
            # 查找一个活跃订阅
            subscription = Subscription.query.join(
                Order, Subscription.order_id == Order.id
            ).filter(
                Subscription.is_active == True,
                Order.status == OrderStatus.COMPLETED
            ).first()
            
            if not subscription:
                logger.warning("没有找到活跃订阅进行测试")
                return False
            
            logger.info(f"找到测试订阅: {subscription.id}")
            
            # 创建测试续费任务
            test_renewal_task = RenewalTask(
                subscription_id=subscription.id,
                renewal_months=1,
                original_expiry=datetime.utcnow(),
                new_expiry=datetime.utcnow() + timedelta(days=30),
                status=RenewalTaskStatus.PENDING
            )
            
            db.session.add(test_renewal_task)
            db.session.commit()
            
            logger.info(f"创建了测试续费任务 {test_renewal_task.id}")
            
            # 测试删除订阅
            subscription_service = SubscriptionService()
            subscription_service.delete_subscription_and_order(subscription)
            db.session.commit()
            
            logger.info("✅ 订阅删除成功")
            
            # 验证续费任务是否被删除
            remaining_task = RenewalTask.query.get(test_renewal_task.id)
            if remaining_task is None:
                logger.info("✅ 续费任务记录已正确删除")
                return True
            else:
                logger.warning("⚠️ 续费任务记录未被删除")
                return False
            
        except Exception as e:
            logger.error(f"测试过程中发生错误: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return False

if __name__ == "__main__":
    logger.info("=" * 60)
    logger.info("订阅覆盖功能测试")
    logger.info("=" * 60)
    
    # 测试订阅覆盖
    if test_subscription_override():
        logger.info("✅ 订阅覆盖测试通过")
    else:
        logger.error("❌ 订阅覆盖测试失败")
    
    logger.info("-" * 60)
    
    # 测试手动删除
    if test_manual_deletion():
        logger.info("✅ 手动删除测试通过")
    else:
        logger.error("❌ 手动删除测试失败")
    
    logger.info("=" * 60)
    logger.info("测试完成")
    logger.info("=" * 60)
