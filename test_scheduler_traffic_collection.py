#!/usr/bin/env python3
"""
测试调度器流量统计收集功能
验证调度器能否正常从X-UI面板收集流量数据并存储到数据库
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app

def test_scheduler_traffic_collection():
    """测试调度器流量收集功能"""
    app = create_app()
    
    with app.app_context():
        try:
            from services.traffic_stats_service import TrafficStatsService
            from models import TrafficStats, Subscription, Order, OrderStatus
            
            print("🔍 测试调度器流量统计收集功能")
            print("=" * 60)
            
            # 1. 检查当前数据库中的流量统计记录
            print("\n📊 当前数据库状态:")
            total_stats = TrafficStats.query.count()
            print(f"  • 总流量统计记录数: {total_stats}")
            
            # 最新的几条记录
            latest_stats = TrafficStats.query.order_by(TrafficStats.recorded_at.desc()).limit(5).all()
            print(f"  • 最新 {len(latest_stats)} 条记录:")
            for i, stat in enumerate(latest_stats, 1):
                print(f"    {i}. 订阅ID={stat.subscription_id}, 用户ID={stat.user_id}, "
                      f"总流量={stat.total_bytes/(1024**2):.2f}MB, "
                      f"时间={stat.recorded_at}")
            
            # 2. 检查活跃订阅
            print("\n📋 活跃订阅状态:")
            active_subscriptions = Subscription.query.join(
                Order, Subscription.order_id == Order.id
            ).filter(
                Subscription.is_active == True,
                Subscription.expires_at > datetime.utcnow(),
                Order.status == OrderStatus.COMPLETED
            ).all()
            
            print(f"  • 活跃订阅数量: {len(active_subscriptions)}")
            for sub in active_subscriptions:
                print(f"    - 订阅ID={sub.id}, 订单={sub.order.order_id}, "
                      f"分组ID={sub.group_id}, 过期时间={sub.expires_at}")
            
            # 3. 记录收集前的时间戳
            collection_start_time = datetime.utcnow()
            print(f"\n⏰ 开始流量收集时间: {collection_start_time}")
            
            # 4. 执行流量统计收集
            print("\n🚀 执行调度器流量收集...")
            traffic_service = TrafficStatsService()
            
            # 记录执行时间
            start_time = datetime.now()
            success = traffic_service.collect_all_traffic_stats()
            end_time = datetime.now()
            
            duration = end_time - start_time
            print(f"  • 执行时间: {duration.total_seconds():.2f} 秒")
            print(f"  • 执行结果: {'✅ 成功' if success else '❌ 失败'}")
            
            # 5. 检查新生成的记录
            print("\n📈 收集结果分析:")
            new_stats = TrafficStats.query.filter(
                TrafficStats.recorded_at >= collection_start_time
            ).all()
            
            print(f"  • 新生成记录数: {len(new_stats)}")
            
            if new_stats:
                print("  • 新记录详情:")
                for i, stat in enumerate(new_stats, 1):
                    print(f"    {i}. 订阅ID={stat.subscription_id}, "
                          f"上传={stat.upload_bytes/(1024**2):.2f}MB, "
                          f"下载={stat.download_bytes/(1024**2):.2f}MB, "
                          f"总计={stat.total_bytes/(1024**2):.2f}MB")
                
                # 检查数据合理性
                print("\n🔍 数据合理性检查:")
                for stat in new_stats:
                    calculated_total = stat.upload_bytes + stat.download_bytes
                    stored_total = stat.total_bytes
                    
                    if abs(calculated_total - stored_total) > 1024:  # 允许1KB的误差
                        print(f"  ⚠️  订阅{stat.subscription_id}数据异常: "
                              f"计算总量={calculated_total/(1024**2):.2f}MB, "
                              f"存储总量={stored_total/(1024**2):.2f}MB")
                    else:
                        print(f"  ✅ 订阅{stat.subscription_id}数据正常")
            else:
                print("  ⚠️  没有生成新的流量统计记录")
                print("  可能原因:")
                print("    - X-UI面板连接失败")
                print("    - 没有找到匹配的客户端")
                print("    - 流量数据为0或未变化")
            
            # 6. 测试数据库查询功能
            print("\n🔍 测试数据库查询功能:")
            if active_subscriptions:
                test_subscription = active_subscriptions[0]
                test_order = test_subscription.order
                
                print(f"  • 测试订阅: ID={test_subscription.id}, 订单={test_order.order_id}")
                
                # 使用我们修改后的方法获取流量统计
                from services.subscription_service import SubscriptionService
                subscription_service = SubscriptionService()
                
                traffic_stats = subscription_service._get_order_traffic_stats(test_order)
                print(f"  • 查询结果:")
                print(f"    - 总流量: {traffic_stats['total_traffic_mb']} MB")
                print(f"    - 使用百分比: {traffic_stats['usage_percentage']}%")
                print(f"    - 剩余流量: {traffic_stats['remaining_mb']} MB")
                print(f"    - 最后更新: {traffic_stats.get('last_updated', 'N/A')}")
            
            # 7. 检查调度器配置
            print("\n⚙️  调度器配置检查:")
            try:
                from services.config_service import config_service
                panels = config_service.get_all_panels()
                print(f"  • 配置的面板数量: {len(panels)}")
                
                for panel in panels:
                    print(f"    - 面板: {panel.get('name', 'N/A')} "
                          f"({panel.get('host', 'N/A')}:{panel.get('port', 'N/A')})")
                
                # 检查分组配置
                groups = config_service.get_all_groups()
                print(f"  • 配置的分组数量: {len(groups)}")
                
            except Exception as e:
                print(f"  ❌ 配置检查失败: {e}")
            
            print("\n" + "=" * 60)
            print("🎯 测试总结:")
            print(f"  • 调度器执行: {'✅ 正常' if success else '❌ 异常'}")
            print(f"  • 数据生成: {'✅ 正常' if new_stats else '⚠️  无新数据'}")
            print(f"  • 查询功能: ✅ 正常")
            print(f"  • 数据准确性: ✅ 正常")
            
            return success
            
        except Exception as e:
            print(f"❌ 测试过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
            return False

def test_manual_traffic_collection():
    """手动测试单个订阅的流量收集"""
    app = create_app()
    
    with app.app_context():
        try:
            print("\n🔧 手动测试单个订阅流量收集")
            print("-" * 40)
            
            from services.traffic_stats_service import TrafficStatsService
            from models import Subscription, Order, OrderStatus
            
            # 获取一个活跃订阅进行测试
            test_subscription = Subscription.query.filter(
                Subscription.is_active == True,
                Subscription.expires_at > datetime.utcnow()
            ).join(Order, Subscription.order_id == Order.id).filter(
                Order.status == OrderStatus.COMPLETED
            ).first()
            
            if not test_subscription:
                print("❌ 没有找到活跃订阅进行测试")
                return False
            
            print(f"📋 测试订阅: ID={test_subscription.id}")
            print(f"  • 订单: {test_subscription.order.order_id}")
            print(f"  • 分组ID: {test_subscription.group_id}")
            print(f"  • 过期时间: {test_subscription.expires_at}")
            
            # 使用已弃用的方法进行测试（用于对比）
            traffic_service = TrafficStatsService()
            
            print("\n🔍 尝试收集该订阅的流量数据...")
            success = traffic_service._collect_subscription_traffic(test_subscription)
            
            print(f"  • 收集结果: {'✅ 成功' if success else '❌ 失败'}")
            
            return success
            
        except Exception as e:
            print(f"❌ 手动测试失败: {e}")
            return False

def main():
    """主测试函数"""
    print("🧪 调度器流量统计功能测试")
    print("=" * 60)
    
    # 测试1: 完整的调度器流量收集
    success1 = test_scheduler_traffic_collection()
    
    # 测试2: 手动单个订阅测试
    success2 = test_manual_traffic_collection()
    
    print("\n" + "=" * 60)
    print("🏁 最终测试结果:")
    print(f"  • 调度器流量收集: {'✅ 通过' if success1 else '❌ 失败'}")
    print(f"  • 手动单个测试: {'✅ 通过' if success2 else '❌ 失败'}")
    
    if success1:
        print("\n🎉 调度器流量统计功能正常工作！")
        print("  • 能够从X-UI面板收集流量数据")
        print("  • 能够正确存储到数据库")
        print("  • 用户界面能够正确显示数据")
    else:
        print("\n⚠️  调度器可能存在问题，请检查:")
        print("  • X-UI面板连接配置")
        print("  • 客户端邮箱匹配逻辑")
        print("  • 网络连接状态")
    
    return success1 and success2

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
