#!/usr/bin/env python3
"""
测试流量基准None值修复
验证add_baseline_traffic方法不再出现NoneType和int相加的错误
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_baseline_none_fix():
    """测试流量基准None值修复"""
    try:
        from app import create_app
        from models import db, User, Order, Subscription, OrderStatus, NodeType
        from models.subscription_traffic_baseline import SubscriptionTrafficBaseline
        
        print("🧪 测试流量基准None值修复...")
        
        # 创建应用上下文
        app = create_app()
        with app.app_context():
            
            # 1. 创建测试数据
            print("\n1️⃣ 创建测试数据...")
            
            # 创建测试用户
            test_user = User(
                username=f"baseline_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                email=f"baseline_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}@example.com",
                password_hash="test_hash"
            )
            db.session.add(test_user)
            db.session.flush()
            
            # 创建测试订单
            test_order = Order(
                order_id=f"BASELINE_TEST_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                user_id=test_user.id,
                customer_name="基准测试用户",
                customer_email=test_user.email,
                node_type=NodeType.VLESS,
                duration_days=30,
                traffic_limit_gb=100,
                price=29.99,
                status=OrderStatus.COMPLETED,
                expires_at=datetime.utcnow() + timedelta(days=30)
            )
            db.session.add(test_order)
            db.session.flush()
            
            # 创建测试订阅
            import secrets
            test_subscription = Subscription(
                order_id=test_order.id,
                subscription_token=secrets.token_urlsafe(32),
                is_active=True,
                expires_at=test_order.expires_at
            )
            db.session.add(test_subscription)
            db.session.flush()
            
            print(f"   ✅ 创建测试订阅: {test_subscription.id}")
            
            # 2. 测试get_or_create_baseline方法
            print("\n2️⃣ 测试get_or_create_baseline方法...")
            
            baseline = SubscriptionTrafficBaseline.get_or_create_baseline(test_subscription.id)
            
            print(f"   📋 基准记录ID: {baseline.id if baseline.id else '新创建'}")
            print(f"   📊 初始上传流量: {baseline.baseline_upload_bytes}")
            print(f"   📊 初始下载流量: {baseline.baseline_download_bytes}")
            print(f"   📊 初始总流量: {baseline.baseline_total_bytes}")
            
            # 验证初始值不为None
            if baseline.baseline_upload_bytes is not None and baseline.baseline_download_bytes is not None and baseline.baseline_total_bytes is not None:
                print("   ✅ 初始值正确设置，不为None")
            else:
                print("   ❌ 初始值仍为None")
                return False
            
            # 3. 测试add_baseline_traffic方法
            print("\n3️⃣ 测试add_baseline_traffic方法...")
            
            try:
                # 添加流量数据
                test_upload = 50 * 1024 * 1024  # 50MB
                test_download = 150 * 1024 * 1024  # 150MB
                test_total = test_upload + test_download  # 200MB
                
                print(f"   ➕ 添加流量: 上传 {test_upload / (1024**2):.2f} MB, 下载 {test_download / (1024**2):.2f} MB")
                
                baseline.add_baseline_traffic(test_upload, test_download, test_total)
                
                print(f"   📈 更新后上传流量: {baseline.baseline_upload_bytes / (1024**2):.2f} MB")
                print(f"   📈 更新后下载流量: {baseline.baseline_download_bytes / (1024**2):.2f} MB")
                print(f"   📈 更新后总流量: {baseline.baseline_total_bytes / (1024**2):.2f} MB")
                
                # 验证计算正确
                if (baseline.baseline_upload_bytes == test_upload and 
                    baseline.baseline_download_bytes == test_download and 
                    baseline.baseline_total_bytes == test_total):
                    print("   ✅ 流量累加计算正确")
                else:
                    print("   ❌ 流量累加计算错误")
                    return False
                
                # 4. 测试多次累加
                print("\n4️⃣ 测试多次累加...")
                
                # 再次添加流量
                additional_upload = 25 * 1024 * 1024  # 25MB
                additional_download = 75 * 1024 * 1024  # 75MB
                additional_total = additional_upload + additional_download  # 100MB
                
                print(f"   ➕ 再次添加流量: 上传 {additional_upload / (1024**2):.2f} MB, 下载 {additional_download / (1024**2):.2f} MB")
                
                baseline.add_baseline_traffic(additional_upload, additional_download, additional_total)
                
                expected_upload = test_upload + additional_upload
                expected_download = test_download + additional_download
                expected_total = test_total + additional_total
                
                print(f"   📈 最终上传流量: {baseline.baseline_upload_bytes / (1024**2):.2f} MB")
                print(f"   📈 最终下载流量: {baseline.baseline_download_bytes / (1024**2):.2f} MB")
                print(f"   📈 最终总流量: {baseline.baseline_total_bytes / (1024**2):.2f} MB")
                
                # 验证多次累加正确
                if (baseline.baseline_upload_bytes == expected_upload and 
                    baseline.baseline_download_bytes == expected_download and 
                    baseline.baseline_total_bytes == expected_total):
                    print("   ✅ 多次累加计算正确")
                else:
                    print("   ❌ 多次累加计算错误")
                    return False
                
                # 提交到数据库
                db.session.commit()
                print("   ✅ 数据已提交到数据库")
                
                print("\n🎉 所有测试通过！流量基准None值问题已修复")
                return True
                
            except Exception as e:
                print(f"   ❌ add_baseline_traffic方法测试失败: {e}")
                return False
                
    except Exception as e:
        logger.error(f"测试失败: {e}")
        return False

def test_existing_baseline():
    """测试现有基准记录的处理"""
    try:
        from app import create_app
        from models import db, User, Order, Subscription, OrderStatus, NodeType
        from models.subscription_traffic_baseline import SubscriptionTrafficBaseline
        
        print("\n🔄 测试现有基准记录的处理...")
        
        app = create_app()
        with app.app_context():
            
            # 查找现有的基准记录
            existing_baseline = SubscriptionTrafficBaseline.query.first()
            
            if existing_baseline:
                print(f"   📋 找到现有基准记录: 订阅 {existing_baseline.subscription_id}")
                print(f"   📊 当前流量: {existing_baseline.baseline_total_bytes / (1024**2):.2f} MB")
                
                # 测试对现有记录的累加
                try:
                    test_upload = 10 * 1024 * 1024  # 10MB
                    test_download = 20 * 1024 * 1024  # 20MB
                    test_total = test_upload + test_download  # 30MB
                    
                    original_total = existing_baseline.baseline_total_bytes
                    
                    existing_baseline.add_baseline_traffic(test_upload, test_download, test_total)
                    
                    if existing_baseline.baseline_total_bytes == original_total + test_total:
                        print("   ✅ 现有记录累加正确")
                        db.session.rollback()  # 回滚测试更改
                        return True
                    else:
                        print("   ❌ 现有记录累加错误")
                        db.session.rollback()
                        return False
                        
                except Exception as e:
                    print(f"   ❌ 现有记录测试失败: {e}")
                    db.session.rollback()
                    return False
            else:
                print("   ℹ️  没有找到现有基准记录，跳过此测试")
                return True
                
    except Exception as e:
        logger.error(f"现有基准记录测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始流量基准None值修复测试")
    
    # 测试1: 新基准记录
    success1 = test_baseline_none_fix()
    
    # 测试2: 现有基准记录
    success2 = test_existing_baseline()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！流量基准None值问题修复成功")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
        sys.exit(1)
