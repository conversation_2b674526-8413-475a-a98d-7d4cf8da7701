#!/usr/bin/env python3
"""
测试管理员删除订阅API
验证通过API删除订阅时不再出现约束错误
"""

import sys
import os
import json
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_admin_delete_api():
    """测试管理员删除订阅API"""
    try:
        from app import create_app
        from models import db, User, Order, Subscription, OrderStatus, NodeType, UserRole
        from models.subscription_traffic_baseline import SubscriptionTrafficBaseline
        
        print("🔧 测试管理员删除订阅API...")
        
        app = create_app()
        with app.test_client() as client:
            with app.app_context():
                
                # 1. 创建测试数据
                print("\n1️⃣ 创建测试数据...")
                
                # 创建管理员用户
                admin_user = User(
                    username=f"test_admin_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    email=f"admin_{datetime.now().strftime('%Y%m%d_%H%M%S')}@test.com",
                    password_hash="admin_hash",
                    role=UserRole.ADMIN
                )
                db.session.add(admin_user)
                db.session.flush()
                
                # 创建测试用户
                test_user = User(
                    username=f"api_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    email=f"api_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}@example.com",
                    password_hash="test_hash"
                )
                db.session.add(test_user)
                db.session.flush()
                
                # 创建测试订单
                test_order = Order(
                    order_id=f"API_TEST_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    user_id=test_user.id,
                    customer_name="API测试用户",
                    customer_email=test_user.email,
                    node_type=NodeType.VLESS,
                    duration_days=30,
                    traffic_limit_gb=100,
                    price=29.99,
                    status=OrderStatus.COMPLETED,
                    expires_at=datetime.utcnow() + timedelta(days=30)
                )
                db.session.add(test_order)
                db.session.flush()
                
                # 创建测试订阅
                import secrets
                test_subscription = Subscription(
                    order_id=test_order.id,
                    subscription_token=secrets.token_urlsafe(32),
                    is_active=True,
                    expires_at=test_order.expires_at
                )
                db.session.add(test_subscription)
                db.session.flush()
                
                # 创建流量基准记录
                test_baseline = SubscriptionTrafficBaseline(
                    subscription_id=test_subscription.id,
                    baseline_upload_bytes=1024 * 1024 * 75,  # 75MB
                    baseline_download_bytes=1024 * 1024 * 225,  # 225MB
                    baseline_total_bytes=1024 * 1024 * 300  # 300MB
                )
                db.session.add(test_baseline)
                db.session.commit()
                
                print(f"   ✅ 创建API测试订阅: {test_subscription.id}")
                print(f"   ✅ 创建流量基准: {test_baseline.baseline_total_bytes / (1024**2):.2f} MB")
                
                # 2. 模拟管理员登录
                print("\n2️⃣ 模拟管理员登录...")
                with client.session_transaction() as sess:
                    sess['user_id'] = admin_user.id
                    sess['username'] = admin_user.username
                    sess['role'] = 'admin'
                
                # 3. 测试软删除API
                print("\n3️⃣ 测试软删除API...")
                response = client.delete(f'/admin/api/subscriptions/{test_subscription.id}/delete')
                
                if response.status_code == 200:
                    result = json.loads(response.data)
                    if result.get('success'):
                        print("   ✅ 软删除API调用成功")
                        print(f"   📝 响应消息: {result.get('message', '')}")
                        
                        # 验证软删除结果
                        updated_subscription = db.session.get(Subscription, test_subscription.id)
                        if updated_subscription and not updated_subscription.is_active:
                            print("   ✅ 订阅已被软删除（is_active=False）")
                        else:
                            print("   ❌ 软删除验证失败")
                            return False
                    else:
                        print(f"   ❌ 软删除API返回失败: {result.get('message', '')}")
                        return False
                else:
                    print(f"   ❌ 软删除API调用失败，状态码: {response.status_code}")
                    print(f"   📝 响应内容: {response.data.decode()}")
                    return False
                
                # 4. 测试硬删除API
                print("\n4️⃣ 测试硬删除API...")
                response = client.delete(f'/admin/api/subscriptions/{test_subscription.id}/delete?hard_delete=true')
                
                if response.status_code == 200:
                    result = json.loads(response.data)
                    if result.get('success'):
                        print("   ✅ 硬删除API调用成功")
                        print(f"   📝 响应消息: {result.get('message', '')}")
                        
                        # 验证硬删除结果
                        remaining_subscription = db.session.get(Subscription, test_subscription.id)
                        remaining_baseline = SubscriptionTrafficBaseline.query.filter_by(
                            subscription_id=test_subscription.id
                        ).first()
                        remaining_order = db.session.get(Order, test_order.id)
                        
                        if remaining_subscription is None:
                            print("   ✅ 订阅记录已被硬删除")
                        else:
                            print("   ❌ 订阅记录仍然存在")
                            return False
                            
                        if remaining_baseline is None:
                            print("   ✅ 流量基准记录已被删除")
                        else:
                            print("   ❌ 流量基准记录仍然存在")
                            return False
                            
                        if remaining_order is None:
                            print("   ✅ 订单记录已被删除")
                        else:
                            print("   ❌ 订单记录仍然存在")
                            return False
                            
                        print("\n🎉 API测试完成！硬删除功能正常")
                        return True
                        
                    else:
                        print(f"   ❌ 硬删除API返回失败: {result.get('message', '')}")
                        return False
                else:
                    print(f"   ❌ 硬删除API调用失败，状态码: {response.status_code}")
                    print(f"   📝 响应内容: {response.data.decode()}")
                    return False
                    
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_scenario():
    """测试错误场景：删除不存在的订阅"""
    try:
        from app import create_app
        from models import db, User, UserRole
        
        print("\n🚨 测试错误场景...")
        
        app = create_app()
        with app.test_client() as client:
            with app.app_context():
                
                # 创建管理员用户
                admin_user = User(
                    username=f"test_admin_error_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    email=f"admin_error_{datetime.now().strftime('%Y%m%d_%H%M%S')}@test.com",
                    password_hash="admin_hash",
                    role=UserRole.ADMIN
                )
                db.session.add(admin_user)
                db.session.commit()
                
                # 模拟管理员登录
                with client.session_transaction() as sess:
                    sess['user_id'] = admin_user.id
                    sess['username'] = admin_user.username
                    sess['role'] = 'admin'
                
                # 尝试删除不存在的订阅
                response = client.delete('/admin/api/subscriptions/99999/delete')
                
                if response.status_code == 404:
                    print("   ✅ 正确处理了不存在的订阅（返回404）")
                    return True
                else:
                    result = json.loads(response.data) if response.data else {}
                    print(f"   ❌ 错误场景处理异常，状态码: {response.status_code}")
                    print(f"   📝 响应: {result}")
                    return False
                    
    except Exception as e:
        print(f"❌ 错误场景测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始管理员删除订阅API测试")
    
    # 测试1: 正常删除流程
    success1 = test_admin_delete_api()
    
    # 测试2: 错误场景
    success2 = test_error_scenario()
    
    if success1 and success2:
        print("\n🎉 所有API测试通过！删除订阅功能修复成功")
        sys.exit(0)
    else:
        print("\n❌ 部分API测试失败，需要进一步检查")
        sys.exit(1)
