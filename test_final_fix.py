#!/usr/bin/env python3
"""
测试最终修复效果
"""

from app import create_app

def test_traffic_display():
    """测试流量显示修复"""
    app = create_app()
    with app.app_context():
        from services.subscription_service import SubscriptionService
        service = SubscriptionService()
        
        # 获取用户订阅
        subs = service.get_user_subscriptions(1)
        print(f'获取到 {len(subs)} 个订阅')
        
        for sub in subs:
            print(f"\n订阅: {sub['order_id']}")
            print(f"活跃状态: {sub['is_active']}")
            
            traffic = sub['traffic_stats']
            print(f"流量使用: {traffic['usage_percentage']}%")
            print(f"已用流量: {traffic['total_traffic_mb']} MB")
            print(f"流量限制: {traffic['traffic_limit_mb']} MB")
            print(f"剩余流量: {traffic['remaining_mb']} MB")

if __name__ == "__main__":
    test_traffic_display()
