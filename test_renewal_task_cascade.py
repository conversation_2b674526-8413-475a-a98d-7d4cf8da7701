#!/usr/bin/env python3
"""
测试续费任务级联删除修复
"""
import sys
import os
import logging
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import db, Subscription, Order, OrderStatus, RenewalTask, RenewalTaskStatus
from services.subscription_service import SubscriptionService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_renewal_task_cascade_deletion():
    """测试续费任务级联删除"""
    app = create_app()
    
    with app.app_context():
        try:
            logger.info("开始测试续费任务级联删除...")
            
            # 查找一个活跃订阅
            subscription = Subscription.query.join(
                Order, Subscription.order_id == Order.id
            ).filter(
                Subscription.is_active == True,
                Order.status == OrderStatus.COMPLETED
            ).first()
            
            if not subscription:
                logger.warning("没有找到活跃订阅进行测试")
                return False
            
            logger.info(f"找到测试订阅: {subscription.id}")
            
            # 创建测试续费任务
            test_renewal_task = RenewalTask(
                subscription_id=subscription.id,
                renewal_months=1,
                original_expiry=datetime.utcnow(),
                new_expiry=datetime.utcnow() + timedelta(days=30),
                status=RenewalTaskStatus.PENDING
            )
            
            db.session.add(test_renewal_task)
            db.session.commit()
            
            task_id = test_renewal_task.id
            logger.info(f"创建了测试续费任务 {task_id}")
            
            # 验证续费任务存在
            existing_task = RenewalTask.query.get(task_id)
            if not existing_task:
                logger.error("续费任务创建失败")
                return False
            
            logger.info(f"续费任务 {task_id} 创建成功，关联订阅 {subscription.id}")
            
            # 测试删除订阅（这应该级联删除续费任务）
            subscription_service = SubscriptionService()
            subscription_service.delete_subscription_and_order(subscription)
            db.session.commit()
            
            logger.info("订阅删除完成")
            
            # 验证续费任务是否被级联删除
            remaining_task = RenewalTask.query.get(task_id)
            if remaining_task is None:
                logger.info("✅ 续费任务记录已正确级联删除")
                return True
            else:
                logger.error("❌ 续费任务记录未被级联删除")
                return False
            
        except Exception as e:
            logger.error(f"测试过程中发生错误: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return False

def test_renewal_task_foreign_key():
    """测试续费任务外键约束"""
    app = create_app()
    
    with app.app_context():
        try:
            logger.info("开始测试续费任务外键约束...")
            
            # 查找一个活跃订阅
            subscription = Subscription.query.join(
                Order, Subscription.order_id == Order.id
            ).filter(
                Subscription.is_active == True,
                Order.status == OrderStatus.COMPLETED
            ).first()
            
            if not subscription:
                logger.warning("没有找到活跃订阅进行测试")
                return False
            
            logger.info(f"找到测试订阅: {subscription.id}")
            
            # 创建续费任务
            renewal_task = RenewalTask(
                subscription_id=subscription.id,
                renewal_months=3,
                original_expiry=datetime.utcnow(),
                new_expiry=datetime.utcnow() + timedelta(days=90),
                status=RenewalTaskStatus.COMPLETED
            )
            
            db.session.add(renewal_task)
            db.session.commit()
            
            logger.info(f"创建续费任务成功: {renewal_task.id}")
            
            # 验证外键关系
            if renewal_task.subscription:
                logger.info(f"✅ 续费任务正确关联到订阅 {renewal_task.subscription.id}")
            else:
                logger.error("❌ 续费任务未正确关联到订阅")
                return False
            
            # 清理测试数据
            db.session.delete(renewal_task)
            db.session.commit()
            
            logger.info("测试数据清理完成")
            return True
            
        except Exception as e:
            logger.error(f"测试过程中发生错误: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return False

if __name__ == "__main__":
    logger.info("=" * 60)
    logger.info("续费任务级联删除测试")
    logger.info("=" * 60)
    
    # 测试外键约束
    if test_renewal_task_foreign_key():
        logger.info("✅ 续费任务外键约束测试通过")
    else:
        logger.error("❌ 续费任务外键约束测试失败")
    
    logger.info("-" * 60)
    
    # 测试级联删除
    if test_renewal_task_cascade_deletion():
        logger.info("✅ 续费任务级联删除测试通过")
    else:
        logger.error("❌ 续费任务级联删除测试失败")
    
    logger.info("=" * 60)
    logger.info("测试完成")
    logger.info("=" * 60)
