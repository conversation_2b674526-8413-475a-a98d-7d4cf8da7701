#!/usr/bin/env python3
"""
续费功能测试脚本
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import db, User, Order, Subscription, RenewalPricing, OrderStatus
from services.renewal_service import RenewalService
from datetime import datetime, timedelta

def test_renewal_functionality():
    """测试续费功能"""
    app = create_app()
    
    with app.app_context():
        print("=== 续费功能测试 ===")
        
        # 1. 测试续费价格配置
        print("\n1. 测试续费价格配置...")
        try:
            pricing_configs = RenewalPricing.get_active_pricing()
            print(f"找到 {len(pricing_configs)} 个续费价格配置:")
            for config in pricing_configs:
                print(f"  - {config.duration_months}个月: {config.discount_percentage}%折扣")
        except Exception as e:
            print(f"获取续费价格配置失败: {e}")
        
        # 2. 测试续费服务初始化
        print("\n2. 测试续费服务...")
        try:
            renewal_service = RenewalService()
            pricing_data = renewal_service.get_renewal_pricing()
            print(f"续费服务获取到 {len(pricing_data)} 个价格配置")
        except Exception as e:
            print(f"续费服务测试失败: {e}")
        
        # 3. 测试用户活跃订阅查询
        print("\n3. 测试用户订阅查询...")
        try:
            # 查找第一个用户
            user = User.query.first()
            if user:
                print(f"测试用户: {user.username} (ID: {user.id})")
                subscription = renewal_service.get_user_active_subscription(user.id)
                if subscription:
                    print(f"找到活跃订阅: {subscription.id}")
                    print(f"到期时间: {subscription.expires_at}")
                    
                    # 4. 测试续费价格计算
                    print("\n4. 测试续费价格计算...")
                    for duration in [3, 6, 12]:
                        try:
                            price_info = renewal_service.calculate_renewal_price(subscription, duration)
                            print(f"  {duration}个月续费:")
                            print(f"    基础价格: ¥{price_info['base_price']}")
                            print(f"    折扣: {price_info['discount_percentage']}%")
                            print(f"    最终价格: ¥{price_info['final_price']}")
                        except Exception as e:
                            print(f"    计算{duration}个月续费价格失败: {e}")
                else:
                    print("用户没有活跃订阅")
            else:
                print("没有找到用户")
        except Exception as e:
            print(f"用户订阅查询失败: {e}")
        
        # 5. 测试数据库表结构
        print("\n5. 测试数据库表结构...")
        try:
            # 检查续费相关表是否存在
            tables_to_check = ['renewal_pricing', 'renewal_tasks']
            for table_name in tables_to_check:
                result = db.session.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
                if result.fetchone():
                    print(f"  ✓ 表 {table_name} 存在")
                else:
                    print(f"  ✗ 表 {table_name} 不存在")
            
            # 检查orders表的新字段
            result = db.session.execute("PRAGMA table_info(orders)")
            columns = [row[1] for row in result.fetchall()]
            if 'order_type' in columns:
                print("  ✓ orders表包含order_type字段")
            else:
                print("  ✗ orders表缺少order_type字段")
                
            if 'parent_subscription_id' in columns:
                print("  ✓ orders表包含parent_subscription_id字段")
            else:
                print("  ✗ orders表缺少parent_subscription_id字段")
                
        except Exception as e:
            print(f"数据库表结构检查失败: {e}")
        
        print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_renewal_functionality()
