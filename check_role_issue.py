#!/usr/bin/env python3
"""
检查用户角色问题
"""
import sqlite3

def check_role_issue():
    try:
        conn = sqlite3.connect('instance/node_sales.db')
        cursor = conn.cursor()
        
        # 查看表结构
        cursor.execute('PRAGMA table_info(users)')
        columns = cursor.fetchall()
        print('用户表结构:')
        for col in columns:
            print(f'  {col}')
        
        # 查看有问题的用户
        cursor.execute('SELECT id, username, role FROM users WHERE role = "admin"')
        problem_users = cursor.fetchall()
        print('\n角色为小写admin的用户:')
        for user in problem_users:
            print(f'  ID: {user[0]}, 用户名: {user[1]}, 角色: {user[2]}')
            
        # 查看所有不同的角色值
        cursor.execute('SELECT DISTINCT role FROM users')
        roles = cursor.fetchall()
        print('\n数据库中所有角色值:')
        for role in roles:
            print(f'  {role[0]}')
            
        conn.close()
        
    except Exception as e:
        print(f"查询失败: {e}")

if __name__ == "__main__":
    check_role_issue()
